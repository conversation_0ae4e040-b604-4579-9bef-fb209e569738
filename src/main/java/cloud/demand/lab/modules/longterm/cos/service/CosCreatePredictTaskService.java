package cloud.demand.lab.modules.longterm.cos.service;

import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryForCreateResp;
import org.springframework.stereotype.Service;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@Service
public interface CosCreatePredictTaskService {

    /**
     * 查询用于创建任务的方案列表及关键信息
     */
    QueryCategoryForCreateResp queryCategoryForCreate(@JsonrpcParam QueryCategoryForCreateReq req);

}
