package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.service.CosCreatePredictTaskService;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryForCreateResp;
import org.springframework.stereotype.Component;

@Component
public class CosCreatePredictTaskServiceImpl implements CosCreatePredictTaskService {


    @Override
    public QueryCategoryForCreateResp queryCategoryForCreate(QueryCategoryForCreateReq req) {
        return null;
    }

}
